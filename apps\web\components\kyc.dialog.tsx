"use client";

import * as React from "react";
import { useForm } from "@tanstack/react-form";
import { Button } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";

export interface KYCDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSubmit?: (data: KYCFormData) => void;
  onCancel?: () => void;
}

export interface KYCFormData {
  firstName: string;
  lastName: string;
}

export function KYCDialog({
  open = true,
  onOpenChange,
  onSubmit,
  onCancel,
}: KYCDialogProps) {
  const form = useForm({
    defaultValues: {
      firstName: "",
      lastName: "",
    },
    onSubmit: async ({ value }) => {
      onSubmit?.(value);
    },
    validators: {
      onChange: ({ value }) => {
        const errors: Record<string, string> = {};

        if (!value.firstName.trim()) {
          errors.firstName = "Ad gereklidir";
        } else if (value.firstName.trim().length < 2) {
          errors.firstName = "Ad en az 2 karakter olmalıdır";
        }

        if (!value.lastName.trim()) {
          errors.lastName = "Soyad gereklidir";
        } else if (value.lastName.trim().length < 2) {
          errors.lastName = "Soyad en az 2 karakter olmalıdır";
        }

        return Object.keys(errors).length > 0 ? errors : undefined;
      },
    },
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit();
        }}
      >
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{"Kimlik Doğrulama (KYC)"}</DialogTitle>
            <DialogDescription>
              {
                "Hesabınızı doğrulamak için aşağıdaki bilgileri eksiksiz doldurun. Bu bilgiler güvenlik amacıyla kullanılacaktır."
              }
            </DialogDescription>
          </DialogHeader>

          {/* ID Card Container */}
          <div className="mx-6 p-6 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200 shadow-lg">
            {/* ID Card Header with Chip */}
            <div className="flex justify-between items-start mb-6">
              <div className="text-sm font-semibold text-blue-800">
                TÜRKİYE CUMHURİYETİ
              </div>
              {/* Chip Element */}
              <div className="w-12 h-8 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-md border border-yellow-700 shadow-sm relative">
                <div className="absolute inset-1 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-sm">
                  <div className="grid grid-cols-3 gap-px h-full p-1">
                    {Array.from({ length: 6 }).map((_, i) => (
                      <div key={i} className="bg-yellow-600 rounded-[1px]" />
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Form Fields */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <form.Field
                name="firstName"
                validators={{
                  onChange: ({ value }) => {
                    if (!value.trim()) return "Ad gereklidir";
                    if (value.trim().length < 2)
                      return "Ad en az 2 karakter olmalıdır";
                    return undefined;
                  },
                }}
              >
                {(field) => (
                  <div className="grid gap-2">
                    <Label
                      htmlFor={field.name}
                      className="text-blue-800 font-medium"
                    >
                      {"Ad"}
                    </Label>
                    <Input
                      id={field.name}
                      name={field.name}
                      value={field.state.value}
                      onChange={(e) => field.handleChange(e.target.value)}
                      onBlur={field.handleBlur}
                      className="bg-white border-blue-300 focus:border-blue-500"
                      required
                    />
                    {field.state.meta.errors.length > 0 && (
                      <span className="text-red-600 text-sm">
                        {field.state.meta.errors[0]}
                      </span>
                    )}
                  </div>
                )}
              </form.Field>

              <form.Field
                name="lastName"
                validators={{
                  onChange: ({ value }) => {
                    if (!value.trim()) return "Soyad gereklidir";
                    if (value.trim().length < 2)
                      return "Soyad en az 2 karakter olmalıdır";
                    return undefined;
                  },
                }}
              >
                {(field) => (
                  <div className="grid gap-2">
                    <Label
                      htmlFor={field.name}
                      className="text-blue-800 font-medium"
                    >
                      {"Soyad"}
                    </Label>
                    <Input
                      id={field.name}
                      name={field.name}
                      value={field.state.value}
                      onChange={(e) => field.handleChange(e.target.value)}
                      onBlur={field.handleBlur}
                      className="bg-white border-blue-300 focus:border-blue-500"
                      required
                    />
                    {field.state.meta.errors.length > 0 && (
                      <span className="text-red-600 text-sm">
                        {field.state.meta.errors[0]}
                      </span>
                    )}
                  </div>
                )}
              </form.Field>
            </div>

            {/* ID Card Footer */}
            <div className="mt-6 pt-4 border-t border-blue-300">
              <div className="text-xs text-blue-700 text-center">
                KİMLİK DOĞRULAMA FORMU
              </div>
            </div>
          </div>

          <DialogFooter>
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
            >
              {([canSubmit, isSubmitting]) => (
                <Button
                  type="submit"
                  variant="primary"
                  disabled={!canSubmit || isSubmitting}
                >
                  {isSubmitting ? "Gönderiliyor..." : "Gönder"}
                </Button>
              )}
            </form.Subscribe>
          </DialogFooter>
        </DialogContent>
      </form>
    </Dialog>
  );
}
