"use client";

import * as React from "react";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Separator } from "@workspace/ui/components/separator";

export interface KYCDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSubmit?: (data: KYCFormData) => void;
  onCancel?: () => void;
}

export interface KYCFormData {
  firstName: string;
  lastName: string;
  nationalId: string;
  phoneNumber: string;
  address: string;
  city: string;
  postalCode: string;
  dateOfBirth: string;
}

export function KYCDialog({
  open = true,
  onOpenChange,
  onSubmit,
  onCancel,
}: KYCDialogProps) {
  const [formData, setFormData] = React.useState<KYCFormData>({
    firstName: "",
    lastName: "",
    nationalId: "",
    phoneNumber: "",
    address: "",
    city: "",
    postalCode: "",
    dateOfBirth: "",
  });

  const handleInputChange = (field: keyof KYCFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit?.(formData);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <form onSubmit={handleSubmit}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{"Kimlik Doğrulama (KYC)"}</DialogTitle>
            <DialogDescription>
              {
                "Hesabınızı doğrulamak için aşağıdaki bilgileri eksiksiz doldurun. Bu bilgiler güvenlik amacıyla kullanılacaktır."
              }
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 mx-6 p-4 rounded-lg bg-muted">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="firstName">{"Ad"}</Label>
                <Input
                  id="firstName"
                  name="firstName"
                  value={formData.firstName}
                  onChange={(e) =>
                    handleInputChange("firstName", e.target.value)
                  }
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="lastName">{"Soyad"}</Label>
                <Input
                  id="lastName"
                  name="lastName"
                  value={formData.lastName}
                  onChange={(e) =>
                    handleInputChange("lastName", e.target.value)
                  }
                  required
                />
              </div>
            </div>

            <Separator />

            <div className="grid gap-2">
              <Label htmlFor="nationalId">{"TC Kimlik No"}</Label>
              <Input
                id="nationalId"
                name="nationalId"
                value={formData.nationalId}
                onChange={(e) =>
                  handleInputChange("nationalId", e.target.value)
                }
                maxLength={11}
                required
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="dateOfBirth">{"Doğum Tarihi"}</Label>
              <Input
                id="dateOfBirth"
                name="dateOfBirth"
                type="date"
                value={formData.dateOfBirth}
                onChange={(e) =>
                  handleInputChange("dateOfBirth", e.target.value)
                }
                required
              />
            </div>

            <Separator />

            <div className="grid gap-2">
              <Label htmlFor="phoneNumber">{"Telefon Numarası"}</Label>
              <Input
                id="phoneNumber"
                name="phoneNumber"
                type="tel"
                value={formData.phoneNumber}
                onChange={(e) =>
                  handleInputChange("phoneNumber", e.target.value)
                }
                placeholder="+90 5XX XXX XX XX"
                required
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="address">{"Adres"}</Label>
              <Input
                id="address"
                name="address"
                value={formData.address}
                onChange={(e) => handleInputChange("address", e.target.value)}
                required
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="city">{"Şehir"}</Label>
                <Input
                  id="city"
                  name="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange("city", e.target.value)}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="postalCode">{"Posta Kodu"}</Label>
                <Input
                  id="postalCode"
                  name="postalCode"
                  value={formData.postalCode}
                  onChange={(e) =>
                    handleInputChange("postalCode", e.target.value)
                  }
                  maxLength={5}
                  required
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button type="submit" variant="primary">
              {"DOĞRULA"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </form>
    </Dialog>
  );
}
