"use client";

import * as React from "react";
import { useForm } from "@tanstack/react-form";
import { Button } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";

export interface KYCDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSubmit?: (data: KYCFormData) => void;
  onCancel?: () => void;
}

export interface KYCFormData {
  firstName: string;
  lastName: string;
}

export function KYCDialog({
  open = true,
  onOpenChange,
  onSubmit,
  onCancel,
}: KYCDialogProps) {
  const form = useForm({
    defaultValues: {
      firstName: "",
      lastName: "",
    },
    onSubmit: async ({ value }) => {
      onSubmit?.(value);
    },
    validators: {
      onChange: ({ value }) => {
        const errors: Record<string, string> = {};

        if (!value.firstName.trim()) {
          errors.firstName = "Ad gereklidir";
        } else if (value.firstName.trim().length < 2) {
          errors.firstName = "Ad en az 2 karakter olmalıdır";
        }

        if (!value.lastName.trim()) {
          errors.lastName = "Soyad gereklidir";
        } else if (value.lastName.trim().length < 2) {
          errors.lastName = "Soyad en az 2 karakter olmalıdır";
        }

        return Object.keys(errors).length > 0 ? errors : undefined;
      },
    },
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit();
        }}
      >
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{"Kimlik Doğrulama (KYC)"}</DialogTitle>
            <DialogDescription>
              {
                "Hesabınızı doğrulamak için aşağıdaki bilgileri eksiksiz doldurun. Bu bilgiler güvenlik amacıyla kullanılacaktır."
              }
            </DialogDescription>
          </DialogHeader>

          {/* ID Card Container */}
          <div className="mx-auto p-6 max-w-[400px] rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-muted-foreground shadow-md">
            {/* ID Card Header with Chip */}

            {/* ID Card Content with Photo and Fields */}
            <div className="flex gap-6">
              {/* Photo Rectangle */}
              <div className="w-28 h-32 bg-gray-200 border-2 border-gray-300 rounded-md flex items-center justify-center flex-shrink-0"></div>

              {/* Form Fields - Vertical Layout */}
              <div className="flex-1 space-y-4">
                <form.Field
                  name="firstName"
                  validators={{
                    onChange: ({ value }) => {
                      if (!value.trim()) return "Ad gereklidir";
                      if (value.trim().length < 2)
                        return "Ad en az 2 karakter olmalıdır";
                      return undefined;
                    },
                  }}
                >
                  {(field) => (
                    <div className="grid gap-2">
                      <Label htmlFor={field.name}>{"Ad"}</Label>
                      <Input
                        id={field.name}
                        name={field.name}
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        required
                      />
                      {field.state.meta.errors.length > 0 && (
                        <span className="text-red-600 text-sm">
                          {field.state.meta.errors[0]}
                        </span>
                      )}
                    </div>
                  )}
                </form.Field>

                <form.Field
                  name="lastName"
                  validators={{
                    onChange: ({ value }) => {
                      if (!value.trim()) return "Soyad gereklidir";
                      if (value.trim().length < 2)
                        return "Soyad en az 2 karakter olmalıdır";
                      return undefined;
                    },
                  }}
                >
                  {(field) => (
                    <div className="grid gap-2">
                      <Label htmlFor={field.name}>{"Soyad"}</Label>
                      <Input
                        id={field.name}
                        name={field.name}
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        required
                      />
                      {field.state.meta.errors.length > 0 && (
                        <span className="text-red-600 text-sm">
                          {field.state.meta.errors[0]}
                        </span>
                      )}
                    </div>
                  )}
                </form.Field>
              </div>
            </div>

            <div className="flex justify-between">
              {/* KYC Status Pill */}
              <div className="mt-6 flex justify-center">
                <form.Subscribe
                  selector={(state) => [state.isValid, state.isDirty]}
                >
                  {([isValid, isDirty]) => (
                    <div
                      className={`text-shadow-none px-5 py-2 rounded-full text-xs font-medium ${
                        !isDirty
                          ? "bg-orange-300 border border-orange-400 text-gray-600"
                          : isValid
                            ? "bg-green-100 text-green-700 border border-green-300"
                            : "bg-red-100 text-red-700 border border-red-300"
                      }`}
                    >
                      {!isDirty
                        ? "KYC Bekliyor"
                        : isValid
                          ? "KYC Hazır"
                          : "KYC Eksik"}
                    </div>
                  )}
                </form.Subscribe>
              </div>
            </div>
          </div>

          <DialogFooter>
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
            >
              {([canSubmit, isSubmitting]) => (
                <Button
                  type="submit"
                  variant="primary"
                  disabled={!canSubmit || isSubmitting}
                >
                  {isSubmitting ? "GÖNDERILIYOR..." : "GÖNDER"}
                </Button>
              )}
            </form.Subscribe>
          </DialogFooter>
        </DialogContent>
      </form>
    </Dialog>
  );
}
